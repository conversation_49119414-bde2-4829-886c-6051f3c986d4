
467b5f91b765b281d73304f3057feccc95ce666b	{"key":"{\"terser\":\"4.8.1\",\"node_version\":\"v18.20.8\",\"terser-webpack-plugin\":\"1.4.6\",\"terser-webpack-plugin-options\":{\"test\":new RegExp(\"\\\\.m?js(\\\\?.*)?$\", \"i\"),\"chunkFilter\":() => true,\"warningsFilter\":() => true,\"extractComments\":false,\"sourceMap\":false,\"cache\":true,\"cacheKeys\":defaultCacheKeys => defaultCacheKeys,\"parallel\":true,\"include\":undefined,\"exclude\":undefined,\"minify\":undefined,\"terserOptions\":{\"output\":{\"comments\":new RegExp(\"^\\\\**!|@preserve|@license|@cc_on\", \"i\")},\"compress\":{\"arrows\":false,\"collapse_vars\":false,\"comparisons\":false,\"computed_props\":false,\"hoist_funs\":false,\"hoist_props\":false,\"hoist_vars\":false,\"inline\":false,\"loops\":false,\"negate_iife\":false,\"properties\":false,\"reduce_funcs\":false,\"reduce_vars\":false,\"switches\":false,\"toplevel\":false,\"typeofs\":false,\"booleans\":true,\"if_return\":true,\"sequences\":true,\"unused\":true,\"conditionals\":true,\"dead_code\":true,\"evaluate\":true,\"drop_console\":false,\"drop_debugger\":true},\"mangle\":{\"safari10\":true}}},\"hash\":\"50293ca472b41b31e44a1846e45bbb4a\"}","integrity":"sha512-rr3f0YWjwvJ8Dp1Vz6Rh0E94Y16Y1qXyT2GS5YL6wQPI3r0vi7VqtUA2n/ERvWnTP8bmI0TgW6mNrU9cGFQbFA==","time":1753152111593,"size":336651}