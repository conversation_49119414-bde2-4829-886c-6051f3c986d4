{"remainingRequest": "D:\\project\\scrm\\front\\mobile2\\node_modules\\thread-loader\\dist\\cjs.js!D:\\project\\scrm\\front\\mobile2\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\scrm\\front\\mobile2\\src\\utils\\voiceCall.js", "dependencies": [{"path": "D:\\project\\scrm\\front\\mobile2\\src\\utils\\voiceCall.js", "mtime": 1753164839847}, {"path": "D:\\project\\scrm\\front\\mobile2\\babel.config.js", "mtime": 1750650120014}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751130691202}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751130691203}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751130701171}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}