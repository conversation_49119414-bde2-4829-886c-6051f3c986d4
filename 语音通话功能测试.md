# 语音通话功能测试指南

## 测试环境要求

1. **企业微信环境**: 必须在企业微信客户端中打开页面
2. **权限配置**: 确保企业微信应用已配置 `openEnterpriseChat` 权限
3. **客户数据**: 测试客户必须有有效的 `externalUserid`

## 测试步骤

### 1. 基础功能测试

1. 打开拨打电话SOP页面
2. 确认页面显示客户列表
3. 检查每个客户是否显示两个按钮：
   - 📞 拨打电话（橙色）
   - 🎤 语音通话（绿色）

### 2. 语音通话按钮显示逻辑测试

**测试用例1**: 客户有 `externalUserid`
- **预期结果**: 显示"语音通话"按钮

**测试用例2**: 客户没有 `externalUserid`
- **预期结果**: 不显示"语音通话"按钮

### 3. 语音通话功能测试

**测试用例3**: 在企业微信环境中点击语音通话
1. 点击"🎤 语音通话"按钮
2. **预期结果**: 
   - 打开与客户的企业微信聊天窗口
   - 显示提示消息："已打开聊天窗口，可在其中发起语音通话"

**测试用例4**: 在非企业微信环境中点击语音通话
1. 在普通浏览器中点击"🎤 语音通话"按钮
2. **预期结果**: 显示提示消息："请在企业微信中使用语音通话功能"

### 4. 错误处理测试

**测试用例5**: 客户信息不完整
1. 模拟客户没有 `externalUserid` 的情况
2. **预期结果**: 显示提示消息："客户信息不完整，无法发起语音通话"

**测试用例6**: 企业微信API调用失败
1. 模拟企业微信API返回错误
2. **预期结果**: 显示相应的错误提示消息

### 5. 界面样式测试

**测试用例7**: 按钮样式
1. 检查语音通话按钮是否为绿色渐变背景
2. 检查悬停效果是否正常（按钮上移，阴影加深）
3. 检查按钮布局是否为垂直排列

**测试用例8**: 响应式设计
1. 在不同屏幕尺寸下测试按钮显示
2. 确保按钮在小屏幕上也能正常显示和点击

### 6. 兼容性测试

**测试用例9**: 原有功能不受影响
1. 测试"拨打电话"功能是否正常工作
2. 测试其他SOP功能是否正常工作
3. 确认新功能不影响页面性能

## 调试功能

页面提供了调试面板，可以查看语音通话操作的详细日志：

1. 点击页面右下角的"🐛"按钮打开调试面板
2. 执行语音通话操作
3. 在调试面板中查看相关日志：
   - `开始语音通话`
   - `语音通话窗口打开成功/失败`
   - 相关错误信息

## 预期行为总结

| 场景 | 预期行为 |
|------|----------|
| 有externalUserid的客户 | 显示语音通话按钮 |
| 无externalUserid的客户 | 不显示语音通话按钮 |
| 企业微信环境中点击 | 打开聊天窗口 |
| 非企业微信环境中点击 | 显示环境提示 |
| API调用成功 | 显示成功提示 |
| API调用失败 | 显示错误提示 |

## 注意事项

1. 语音通话功能依赖企业微信的聊天窗口，实际的语音通话操作需要在聊天窗口中完成
2. 确保测试用户有权限访问目标客户
3. 测试时注意观察调试日志，有助于问题排查
