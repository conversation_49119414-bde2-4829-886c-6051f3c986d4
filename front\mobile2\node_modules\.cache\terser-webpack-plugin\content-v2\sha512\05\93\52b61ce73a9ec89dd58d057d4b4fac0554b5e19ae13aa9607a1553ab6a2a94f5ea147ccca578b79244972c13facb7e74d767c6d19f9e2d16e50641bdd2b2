{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-2d0c8db8\"],{5755:function(e,r,o){\"use strict\";function c(){if(!window.wx||!window.wx.invoke)return console.warn(\"企业微信环境不可用\"),!1;const e=navigator.userAgent.toLowerCase(),r=e.includes(\"wxwork\")||e.includes(\"micromessenger\");return!!r||(console.warn(\"不在企业微信环境中\"),!1)}function n(e,r={}){return new Promise(r=>{if(c()){if(!e)return console.error(\"客户ID不能为空\"),void r(!1);try{window.wx.invoke(\"startVoiceCall\",{externalUserIds:e},o=>{\"startVoiceCall:ok\"===o.err_msg?(console.log(\"语音通话请求发送成功（但无法确定通话是否真正建立）\",o),r({success:!0,method:\"direct_call\",canDetectStatus:!1})):(console.warn(\"直接语音通话失败，尝试备用方案\",o),t(e,r))})}catch(o){console.error(\"发起语音通话异常，使用备用方案\",o),t(e,r)}}else r(!1)})}function t(e,r){try{window.wx.invoke(\"openEnterpriseChat\",{userIds:\"\",externalUserIds:e,groupName:\"\",chatId:\"\"},e=>{\"openEnterpriseChat:ok\"===e.err_msg?(console.log(\"成功打开与客户的聊天窗口\",e),r({success:!0,method:\"chat_window\",canDetectStatus:!1})):(console.error(\"打开聊天窗口失败\",e),r({success:!1,method:\"none\",error:e.err_msg}))})}catch(o){console.error(\"备用方案也失败\",o),r(!1)}}function s(e,r={}){return n(e,r)}function a(e,r,o={}){const c={timestamp:(new Date).toISOString(),action:e,externalUserid:r,userAgent:navigator.userAgent,...o};console.log(\"[VoiceCall]\",c)}o.r(r),o.d(r,\"checkVoiceCallSupport\",(function(){return c})),o.d(r,\"startDirectVoiceCall\",(function(){return n})),o.d(r,\"startVoiceCall\",(function(){return s})),o.d(r,\"logVoiceCall\",(function(){return a})),o.d(r,\"VOICE_CALL_ERRORS\",(function(){return i})),o.d(r,\"makeVoiceCall\",(function(){return l}));const i={ENV_NOT_SUPPORTED:\"ENV_NOT_SUPPORTED\",INVALID_CUSTOMER_ID:\"INVALID_CUSTOMER_ID\",WX_API_FAILED:\"WX_API_FAILED\",UNKNOWN_ERROR:\"UNKNOWN_ERROR\"};function u(e){switch(e){case\"direct_call\":return\"语音通话请求已发送（无法确定是否接通）\";case\"chat_window\":return\"已打开聊天窗口，请在聊天中发起语音通话\";default:return\"语音通话功能已触发\"}}async function l(e,r={},o={}){const{onSuccess:n,onError:t,onCancel:l}=o;a(\"voice_call_start\",e,r);try{if(!c()){const r={code:i.ENV_NOT_SUPPORTED,message:\"请在企业微信中使用语音通话功能\"};return t&&t(r),a(\"voice_call_failed\",e,{error:r}),{success:!1,error:r}}const o=await s(e);if(o&&o.success){const c={success:!0,method:o.method,externalUserid:e,customer:r,canDetectStatus:o.canDetectStatus,message:u(o.method)};return n&&n(c),a(\"voice_call_success\",e,c),c}{const r={code:i.WX_API_FAILED,message:o&&o.error?o.error:\"发起语音通话失败，请确保在企业微信中使用\"};return t&&t(r),a(\"voice_call_failed\",e,{error:r}),{success:!1,error:r}}}catch(d){const r={code:i.UNKNOWN_ERROR,message:d.message||\"未知错误\"};return t&&t(r),a(\"voice_call_error\",e,{error:r}),{success:!1,error:r}}}}}]);", "extractedComments": []}