{"remainingRequest": "D:\\project\\scrm\\front\\mobile2\\node_modules\\thread-loader\\dist\\cjs.js!D:\\project\\scrm\\front\\mobile2\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue?vue&type=template&id=71ead75d&scoped=true", "dependencies": [{"path": "D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue", "mtime": 1753164857534}, {"path": "D:\\project\\scrm\\front\\mobile2\\babel.config.js", "mtime": 1750650120014}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751130691202}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751130691202}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751130691203}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751130701171}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751130711384}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751130691202}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751130703090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}