package org.scrm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.scrm.base.core.domain.entity.SysUser;
import org.scrm.base.core.domain.model.LoginUser;
import org.scrm.base.enums.MessageNoticeType;
import org.scrm.config.rabbitmq.RabbitMQSettingConfig;
import org.scrm.domain.WeCorpAccount;
import org.scrm.domain.customer.query.WeCustomersQuery;
import org.scrm.domain.groupmsg.query.WeAddGroupMessageQuery;
import org.scrm.domain.sop.WeSopAttachments;
import org.scrm.domain.sop.WeSopBase;
import org.scrm.domain.sop.WeSopExecuteTarget;
import org.scrm.domain.sop.WeSopExecuteTargetAttachments;
import org.scrm.domain.sop.dto.WeSopPushTaskDto;
import org.scrm.domain.task.query.WeTasksRequest;
import org.scrm.mapper.WeSopExecuteTargetAttachmentsMapper;
import org.scrm.service.*;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;
import java.util.stream.Collectors;
import java.util.HashMap;
import java.text.SimpleDateFormat;
import cn.hutool.core.util.IdUtil;
import org.scrm.domain.media.WeMessageTemplate;
import org.scrm.domain.WeGroupMessageTemplate;
import java.util.UUID;
import java.util.Calendar;

/**
* <AUTHOR>
* @description 针对表【we_sop_execute_target_attachments(目标执行内容)】的数据库操作Service实现
* @createDate 2022-09-13 16:26:00
*/
@Slf4j
@Service
@SuppressWarnings("all")
public class WeSopExecuteTargetAttachmentsServiceImpl extends ServiceImpl<WeSopExecuteTargetAttachmentsMapper, WeSopExecuteTargetAttachments>
implements IWeSopExecuteTargetAttachmentsService {
    @Resource
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private RabbitMQSettingConfig rabbitMQSettingConfig;

    @Autowired
    private IWeSopAttachmentsService iWeSopAttachmentsService;

    @Autowired
    private IWeMessagePushService iWeMessagePushService;

    @Autowired
    private IWeGroupService iWeGroupService;

    @Autowired
    private IWeCustomerService iWeCustomerService;

    @Autowired
    private IWeCorpAccountService iWeCorpAccountService;

    @Autowired
    private IWeSopBaseService iWeSopBaseService;

    @Resource
    private IWeTasksService weTasksService;

    @Autowired
    private IWeSopExecuteTargetService weSopExecuteTargetService;

    // 用于统计拨打电话SOP消息发送次数的计数器
    private static final ThreadLocal<Integer> phoneCallSopMessageCount = new ThreadLocal<>();

    @Override
    public void weChatPushTypeSopTaskTip(String sopBaseId) {
        log.info("[PHONE_CALL_SOP_DEBUG] === weChatPushTypeSopTaskTip开始 ===");
        log.info("[PHONE_CALL_SOP_DEBUG] sopBaseId: {}", sopBaseId);

        // 初始化消息计数器
        phoneCallSopMessageCount.set(0);

        // 先查询SOP基础信息
        try {
            WeSopBase sopBase = iWeSopBaseService.getById(sopBaseId);
            if (sopBase != null) {
                log.info("[PHONE_CALL_SOP_DEBUG] SOP基础信息 - ID: {}, 名称: '{}', 业务类型: {}, 发送类型: {}, 状态: {}",
                        sopBase.getId(), sopBase.getSopName(), sopBase.getBusinessType(), sopBase.getSendType(), sopBase.getSopState());
            } else {
                log.warn("[PHONE_CALL_SOP_DEBUG] 未找到SOP基础信息，sopBaseId: {}", sopBaseId);
                return;
            }
        } catch (Exception e) {
            log.error("[PHONE_CALL_SOP_DEBUG] 查询SOP基础信息失败，sopBaseId: {}", sopBaseId, e);
            return;
        }

        //获取当天要推送的所有任务
        List<WeSopPushTaskDto> weCustomerSopPushTaskDto
                =this.baseMapper.findWeSopPushTaskDtoBySopId(sopBaseId);

        log.info("[PHONE_CALL_SOP_DEBUG] 查询到的企业微信发送任务数量: {}", weCustomerSopPushTaskDto != null ? weCustomerSopPushTaskDto.size() : 0);

        if(CollectionUtil.isNotEmpty(weCustomerSopPushTaskDto)){
            log.info("[PHONE_CALL_SOP_DEBUG] 开始调用weComPushTask发送企业微信群发消息");
            //获取企业微信发送方式任务
            this.weComPushTask(weCustomerSopPushTaskDto);
            log.info("[PHONE_CALL_SOP_DEBUG] weComPushTask调用完成");
        } else {
            log.warn("[PHONE_CALL_SOP_DEBUG] 没有找到符合条件的企业微信发送任务，可能原因：");
            log.warn("[PHONE_CALL_SOP_DEBUG] 1. SOP的sendType不是1（企业微信发送）");
            log.warn("[PHONE_CALL_SOP_DEBUG] 2. 当前时间不在推送时间范围内");
            log.warn("[PHONE_CALL_SOP_DEBUG] 3. 任务已经发送过群发消息（msg_id不为空）");
        }

        log.info("[PHONE_CALL_SOP_DEBUG] === weChatPushTypeSopTaskTip结束 ===");
    }



    private void weComPushTask(List<WeSopPushTaskDto> wecomSendTypes) {

        WeCorpAccount weCorpAccount = iWeCorpAccountService.getCorpAccountByCorpId(null);

        if(CollectionUtil.isNotEmpty(wecomSendTypes) && ObjectUtil.isNotEmpty(weCorpAccount)) {
            //员工id分组
            Map<String, List<WeSopPushTaskDto>> executeWeUserIdGroup
                    = wecomSendTypes.stream().collect(Collectors.groupingBy(WeSopPushTaskDto::getExecuteWeUserId));

            if (CollectionUtil.isNotEmpty(executeWeUserIdGroup)) {

                executeWeUserIdGroup.forEach((k, vv) -> {
                    WeAddGroupMessageQuery messageQuery = new WeAddGroupMessageQuery();
                    messageQuery.setMsgSource(2);
                    messageQuery.setBusinessIds(
                            String.join(",",vv.stream().map(WeSopPushTaskDto::getExcuteTargetAttachId).collect(Collectors.toList()))
                    );
                    messageQuery.setIsAll(false);
                    messageQuery.setIsTask(0);
                    LoginUser loginUser=new LoginUser();
                    loginUser.setUserName(weCorpAccount.getCreateBy());
                    loginUser.setCorpId(weCorpAccount.getCorpId());
                    loginUser.setSysUser(SysUser.builder()
                            .build());
                    messageQuery.setLoginUser(loginUser);
                    messageQuery.setAttachmentsList(
                            iWeSopAttachmentsService.weSopAttachmentsToTemplate(
                                    iWeSopAttachmentsService.listByIds(vv.stream().map(WeSopPushTaskDto::getSopAttachmentId).collect(Collectors.toList()))
                            )
                    );

                    List<WeAddGroupMessageQuery.SenderInfo> senderInfos = new ArrayList<>();

                    WeAddGroupMessageQuery.SenderInfo senderInfo = WeAddGroupMessageQuery.SenderInfo.builder()
                            .userId(k)
                            .build();
                    List<WeSopPushTaskDto> weSopPushCustomerTaskDtos = vv.stream().filter(weSopPushTaskDto ->
                            weSopPushTaskDto.getTargetType() == 1).collect(Collectors.toList());

                    if (CollectionUtil.isNotEmpty(weSopPushCustomerTaskDtos)) {
                        messageQuery.setChatType(1);
                        senderInfo.setCustomerList(
                                weSopPushCustomerTaskDtos.stream().map(WeSopPushTaskDto::getTargetId).collect(Collectors.toList())
                        );
                    }

                    List<WeSopPushTaskDto> weSopPushGroupTaskDtos = vv.stream().filter(weSopPushTaskDto ->
                            weSopPushTaskDto.getTargetType() == 2).collect(Collectors.toList());

                    if (CollectionUtil.isNotEmpty(weSopPushGroupTaskDtos)) {
                        messageQuery.setChatType(2);
                        senderInfo.setChatList(
                                weSopPushGroupTaskDtos.stream().map(WeSopPushTaskDto::getTargetId).collect(Collectors.toList())
                        );
                    }



                    senderInfos.add(senderInfo);
//                        messageQuery.setSendTime(kk);

                    messageQuery.setSenderList(senderInfos);

                    //通知用户群发
                    iWeMessagePushService.officialPushMessage(messageQuery);




                    //时间分组
//                    Map<Date, List<WeSopPushTaskDto>> pushTasks
//                            = v.stream().collect(Collectors.groupingBy(WeSopPushTaskDto::getPushEndTime));
//                    pushTasks.forEach((kk, vv) -> {
//                        WeAddGroupMessageQuery messageQuery = new WeAddGroupMessageQuery();
//                        messageQuery.setMsgSource(2);
//                        messageQuery.setBusinessIds(
//                                String.join(",",vv.stream().map(WeSopPushTaskDto::getExcuteTargetAttachId).collect(Collectors.toList()))
//                        );
//                        messageQuery.setIsAll(false);
//                        messageQuery.setIsTask(0);
//                        LoginUser loginUser=new LoginUser();
//                        loginUser.setUserName(weCorpAccount.getCreateBy());
//                        loginUser.setCorpId(weCorpAccount.getCorpId());
//                        loginUser.setSysUser(SysUser.builder()
//                                .build());
//                        messageQuery.setLoginUser(loginUser);
//                        messageQuery.setAttachmentsList(
//                                iWeSopAttachmentsService.weSopAttachmentsToTemplate(
//                                        iWeSopAttachmentsService.listByIds(vv.stream().map(WeSopPushTaskDto::getSopAttachmentId).collect(Collectors.toList()))
//                                )
//                        );
//
//                        List<WeAddGroupMessageQuery.SenderInfo> senderInfos = new ArrayList<>();
//
//                        WeAddGroupMessageQuery.SenderInfo senderInfo = WeAddGroupMessageQuery.SenderInfo.builder()
//                                .userId(k)
//                                .build();
//                        List<WeSopPushTaskDto> weSopPushCustomerTaskDtos = vv.stream().filter(weSopPushTaskDto ->
//                                weSopPushTaskDto.getTargetType() == 1).collect(Collectors.toList());
//
//                        if (CollectionUtil.isNotEmpty(weSopPushCustomerTaskDtos)) {
//                            messageQuery.setChatType(1);
//                            senderInfo.setCustomerList(
//                                    weSopPushCustomerTaskDtos.stream().map(WeSopPushTaskDto::getTargetId).collect(Collectors.toList())
//                            );
//                        }
//
//                        List<WeSopPushTaskDto> weSopPushGroupTaskDtos = vv.stream().filter(weSopPushTaskDto ->
//                                weSopPushTaskDto.getTargetType() == 2).collect(Collectors.toList());
//
//                        if (CollectionUtil.isNotEmpty(weSopPushGroupTaskDtos)) {
//                            messageQuery.setChatType(2);
//                            senderInfo.setChatList(
//                                    weSopPushGroupTaskDtos.stream().map(WeSopPushTaskDto::getTargetId).collect(Collectors.toList())
//                            );
//                        }
//
//
//
//                        senderInfos.add(senderInfo);
////                        messageQuery.setSendTime(kk);
//
//                        messageQuery.setSenderList(senderInfos);
//
//                        //通知用户群发
//                        iWeMessagePushService.officialPushMessage(messageQuery);
//
//
//                    });


                });

            }
        }

    }


    @Override
    public void manualPushTypeSopTaskTip(boolean isExpiringSoon){

        //获取当天要推送的所有任务
        List<WeSopPushTaskDto> weCustomerSopPushTaskDto
                = this.baseMapper.findWeSopPushTaskDto(null,2,isExpiringSoon);
        log.info("开始推送。。。。。。", weCustomerSopPushTaskDto);

        if(CollectionUtil.isNotEmpty(weCustomerSopPushTaskDto)){

            //每个执行人员
            weCustomerSopPushTaskDto.stream().collect(Collectors.groupingBy(WeSopPushTaskDto::getExecuteWeUserId)).forEach((kk,vv)->{

                Map<Integer, List<WeSopPushTaskDto>> collect = vv.stream().collect(Collectors.groupingBy(WeSopPushTaskDto::getTargetType));

                //客户sop手动推送任务
                this.sopTaskTodayTip(kk,false, collect.get(1),isExpiringSoon);

                //客群sop手动推送任务
                this.sopTaskTodayTip(kk,true,collect.get(2),isExpiringSoon);
            });


        }else {
            log.info("没有数据。。。。。。");

        }

    }

    @Override
    public List<WeSopPushTaskDto> findWeSopPushTaskDtoByWeUserId(String weUserId, Integer targetType, Integer sendType) {
        return this.baseMapper.findWeSopPushTaskDtoByWeUserId(weUserId,targetType,sendType);
    }


    private void sopTaskTodayTip(String executeWeUserId,boolean groupOrCustomer,List<WeSopPushTaskDto> weCustomerSopPushTaskDto,boolean isExpiringSoon){

        if(CollectionUtil.isNotEmpty(weCustomerSopPushTaskDto)) {
            // 检查是否为拨打电话SOP
            boolean isPhoneCallSop = false;
            if (!groupOrCustomer && CollectionUtil.isNotEmpty(weCustomerSopPushTaskDto)) {
                Set<String> sopBaseIds = weCustomerSopPushTaskDto.stream()
                        .map(WeSopPushTaskDto::getSopBaseId)
                        .filter(StringUtils::isNotEmpty)
                        .collect(Collectors.toSet());

                if (CollectionUtil.isNotEmpty(sopBaseIds)) {
                    List<WeSopBase> sopBases = iWeSopBaseService.listByIds(sopBaseIds);
                    isPhoneCallSop = sopBases.stream()
                            .anyMatch(sopBase -> Integer.valueOf(7).equals(sopBase.getBusinessType()));
                }
            }

            log.info("[PHONE_CALL_SOP_DEBUG] sopTaskTodayTip - isPhoneCallSop: {}, groupOrCustomer: {}", isPhoneCallSop, groupOrCustomer);

            // 如果是拨打电话SOP，需要按sopBaseId分组处理，每个SOP发送独立消息
            if (isPhoneCallSop && !groupOrCustomer) {
                log.info("[PHONE_CALL_SOP_DEBUG] 检测到拨打电话SOP，调用handlePhoneCallSopByGroup方法");
                handlePhoneCallSopByGroup(executeWeUserId, weCustomerSopPushTaskDto, isExpiringSoon);
                log.info("[PHONE_CALL_SOP_DEBUG] handlePhoneCallSopByGroup方法执行完成，拨打电话SOP只发送独立消息（包含sopBaseId），不发送混合消息");
                // 拨打电话SOP只发送独立消息，避免重复发送和功能冲突
                return;
            }

            StringBuilder textContent = new StringBuilder();
            if(groupOrCustomer){
                textContent.append("【客群SOP】\r\n");

                if(isExpiringSoon){
                    textContent.append("以下客群的SOP推送时间剩余10分钟。\r\n");
                }else{

                    textContent.append(" 今天有" +
                            weCustomerSopPushTaskDto.stream().map(WeSopPushTaskDto::getTargetId).collect(Collectors.toSet()).size()
                            + "个客群SOP待推送。\r\n");
                }

                // 按sopBaseId分组
                Map<String, List<WeSopPushTaskDto>> sopBaseIdGroupMap = weCustomerSopPushTaskDto.stream()
                        .collect(Collectors.groupingBy(WeSopPushTaskDto::getSopBaseId));
                
                // 遍历每个sopBaseId的任务组，分别触发群发
                sopBaseIdGroupMap.forEach((sopBaseId, tasksForSop) -> {
                    // 按执行人groupBy
                    Map<String, List<WeSopPushTaskDto>> userIdGroupMap = tasksForSop.stream()
                            .collect(Collectors.groupingBy(WeSopPushTaskDto::getExecuteWeUserId));
                    
                    userIdGroupMap.forEach((userId, userTasks) -> {
                        WeAddGroupMessageQuery query = new WeAddGroupMessageQuery();
                        // 设置基本参数
                        query.setMsgSource(8);
                        query.setIsAll(false);
                        query.setAllSend(false);
                        query.setIsTask(0);
                        query.setChatType(2); // 群聊类型
                        
                        // 参数对象
                        Map<String, Object> params = new HashMap<>();
                        query.setParams(params);
                        
                        // 生成唯一ID
                        query.setId(IdUtil.getSnowflakeNextId());
                        
                        // 获取附件列表转为模板
                        List<WeSopAttachments> sopAttachmentsList = iWeSopAttachmentsService.listByIds(
                                userTasks.stream()
                                        .map(WeSopPushTaskDto::getSopAttachmentId)
                                        .map(id -> Long.valueOf(id))
                                        .collect(Collectors.toList())
                        );
                        
                        // 将SOP附件转换为消息模板
                        List<WeMessageTemplate> messageTemplates = iWeSopAttachmentsService.weSopAttachmentsToTemplate(sopAttachmentsList);
                        query.setAttachmentsList(messageTemplates);
                        
                        // 如果有文本内容，设置content字段
                        for (WeMessageTemplate attachment : messageTemplates) {
                            if ("text".equals(attachment.getMsgType())) {
                                String content = attachment.getContent();
                                if (StringUtils.isNotEmpty(content)) {
                                    query.setContent(content);
                                    break;
                                }
                            }
                        }
                        
                        // 构建发送人信息
                        List<WeAddGroupMessageQuery.SenderInfo> senderInfos = new ArrayList<WeAddGroupMessageQuery.SenderInfo>();
                        WeAddGroupMessageQuery.SenderInfo senderInfo = new WeAddGroupMessageQuery.SenderInfo();
                        senderInfo.setUserId(userId);
                        
                        List<String> chatList = userTasks.stream()
                                .map(WeSopPushTaskDto::getTargetId)
                                .collect(Collectors.toList());
                        senderInfo.setChatList(chatList);
                        
                        senderInfos.add(senderInfo);
                        query.setSenderList(senderInfos);
                        
                        // 设置客户查询条件 - 完整版
                        WeCustomersQuery weCustomersQuery = new WeCustomersQuery();
                        
                        // 基本查询条件
                        weCustomersQuery.setUserIds(userId);
                        weCustomersQuery.setUserNames(userId);
                        weCustomersQuery.setIsJoinBlacklist(1);
                        weCustomersQuery.setDataScope(false);
                        weCustomersQuery.setFilterLossCustomer(false);
                        weCustomersQuery.setNoRepeat(false);
                        weCustomersQuery.setNoTagCheck(false);
                        weCustomersQuery.setTagNumber(0);
                        weCustomersQuery.setIsContain(2); // 默认包含其中一个标签
                        
                        // 获取关联的SOP基础信息
                        WeSopBase sopBase = iWeSopBaseService.getById(sopBaseId);
                        
                        // 如果SOP基础配置中已有标签条件，则优先使用
                        if (sopBase != null && sopBase.getWeCustomersQuery() != null) {
                            WeCustomersQuery baseQuery = sopBase.getWeCustomersQuery();
                            if (StringUtils.isNotEmpty(baseQuery.getExcludeTagIds())){
                                weCustomersQuery.setExcludeTagIds(baseQuery.getExcludeTagIds());
                            }
                            // 处理标签条件
                            if (StringUtils.isNotEmpty(baseQuery.getTagIds())) {
                                weCustomersQuery.setTagIds(baseQuery.getTagIds());
                                weCustomersQuery.setTagNames(baseQuery.getTagNames());
                                weCustomersQuery.setTagNumber(baseQuery.getTagNumber());
                                
                                // 标签包含关系
                                Integer isContain = baseQuery.getIsContain();
                                if (isContain != null) {
                                    weCustomersQuery.setIsContain(isContain);
                                } else {
                                    weCustomersQuery.setIsContain(2); // 默认包含其中一个标签
                                }
                            } else {
                                weCustomersQuery.setIsContain(2); // 默认包含其中一个标签
                            }
                            
                            // 处理执行人员条件
                            if (StringUtils.isNotEmpty(baseQuery.getUserIds())) {
                                weCustomersQuery.setUserIds(baseQuery.getUserIds());
                                weCustomersQuery.setUserNames(baseQuery.getUserNames());
                            }
                            
                            // 处理客户类型条件
                            if (baseQuery.getCustomerType() != null) {
                                weCustomersQuery.setCustomerType(baseQuery.getCustomerType());
                            }
                            
                            // 处理性别条件
                            if (baseQuery.getGender() != null) {
                                weCustomersQuery.setGender(baseQuery.getGender());
                            }
                        } else {
                            weCustomersQuery.setIsContain(2); // 默认包含其中一个标签
                        }
                        
                        // 创建WeCustomersOrGroupQuery对象
                        WeGroupMessageTemplate.WeCustomersOrGroupQuery customersOrGroupQuery = new WeGroupMessageTemplate.WeCustomersOrGroupQuery();
                        customersOrGroupQuery.setWeCustomersQuery(weCustomersQuery);
                        query.setWeCustomersOrGroupQuery(customersOrGroupQuery);
                        
                        // 设置weCustomersQuery
                        query.setWeCustomersQuery(weCustomersQuery);
                        
                        // 设置当前用户信息 - 完整版
                        WeCorpAccount weCorpAccount = iWeCorpAccountService.getCorpAccountByCorpId(null);
                        if (weCorpAccount != null) {
                            LoginUser loginUser = new LoginUser();
                            // 填充loginUser基本信息
                            loginUser.setUserName("超管");
                            loginUser.setUserId(1L);
                            loginUser.setCorpId(weCorpAccount.getCorpId());
                            loginUser.setCorpName(weCorpAccount.getCompanyName());
                            // 生成一个简单的token，与正确格式一致
                            loginUser.setToken(UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32));
                            loginUser.setUserType("00");
                            loginUser.setRoles(new HashSet<>());
                            
                            // 设置额外字段，与正常群发保持一致
                            loginUser.setExpireTime(System.currentTimeMillis() + ********); // 12小时
                            loginUser.setLoginTime(System.currentTimeMillis());
                            loginUser.setIpaddr("**********");
                            
                            // 设置SysUser详细信息
                            SysUser sysUser = new SysUser();
                            sysUser.setUserId(1L);
                            sysUser.setUserName("超管");
                            sysUser.setWeUserId("admin");
                            sysUser.setNickName("admin");
                            sysUser.setCheckIsRoot(false);
                            sysUser.setDataScope(1);
                            sysUser.setDelFlag(0);
                            sysUser.setSex("0");
                            sysUser.setStatus("0");
                            sysUser.setUserType("00");
                            sysUser.setRoles(new ArrayList<>());
                            sysUser.setDeptId(0L);
                            
                            // 添加其他必要字段
                            sysUser.setAvatar("https://iyque-1251309172.cos.ap-nanjing.myqcloud.com/2024/07/03/3df8e83a-8db2-493c-ac88-392d5bc8e4f1.png");
                            sysUser.setEmail("");
                            sysUser.setPhoneNumber("");
                            sysUser.setLoginIp("");
                            sysUser.setPassword("$2a$10$B3bRSFj1XXwv4KxszXQQbOUStlwYiwTIlyllHVU6fd9pjDK/g0XQS");
                            
                            // 设置创建时间
                            Calendar calendar = Calendar.getInstance();
                            calendar.set(2022, Calendar.SEPTEMBER, 7, 14, 24, 47); 
                            sysUser.setCreateTime(calendar.getTime());
                            
                            // 不要尝试设置dept字段，让JSON序列化器处理它
                            // 只需要设置正确的参数
                            sysUser.setParams(new HashMap<>());
                            
                            loginUser.setSysUser(sysUser);
                            query.setLoginUser(loginUser);
                        }
                        
                        // 不要使用反射设置weCustomersQuery
                        // Spring会根据JSON注解处理这个引用
                        // 保持默认的weCustomersQuery设置即可
                        
                        // 设置业务ID列表
                        query.setBusinessIds(
                                String.join(",", userTasks.stream()
                                        .map(WeSopPushTaskDto::getExcuteTargetAttachId)
                                        .collect(Collectors.toList()))
                        );
                        
                        // 发送到立即群发队列
                        rabbitTemplate.convertAndSend(rabbitMQSettingConfig.getWeDelayEx(), 
                                rabbitMQSettingConfig.getWeGroupMsgRk(), 
                                JSONObject.toJSONString(query));
                        
                        // 发送消息后，立即更新任务状态为已执行，防止重复执行
                        List<Long> taskIds = userTasks.stream()
                                .map(WeSopPushTaskDto::getExcuteTargetAttachId)
                                .map(Long::valueOf)
                                .collect(Collectors.toList());
                        
                        if (CollectionUtil.isNotEmpty(taskIds)) {
                            // 更新附件状态
                            this.update(
                                WeSopExecuteTargetAttachments.builder()
                                    .executeState(1)  // 更新为已执行状态
                                    .executeTime(new Date())  // 设置实际执行时间
                                    .isTip(isExpiringSoon ? 2 : 1)  // 设置提醒状态
                                    .build(),
                                new LambdaQueryWrapper<WeSopExecuteTargetAttachments>()
                                    .in(WeSopExecuteTargetAttachments::getId, taskIds)
                            );
                            
                            // 查询并获取所有关联的execute_target_id
                            Set<Long> targetIds = new HashSet<>();
                            List<WeSopExecuteTargetAttachments> attachmentsList = this.list(
                                new LambdaQueryWrapper<WeSopExecuteTargetAttachments>()
                                    .in(WeSopExecuteTargetAttachments::getId, taskIds)
                                    .select(WeSopExecuteTargetAttachments::getExecuteTargetId)
                            );
                            
                            if (CollectionUtil.isNotEmpty(attachmentsList)) {
                                for (WeSopExecuteTargetAttachments attachment : attachmentsList) {
                                    targetIds.add(attachment.getExecuteTargetId());
                                }
                                
                                // 更新目标执行对象状态
                                if (CollectionUtil.isNotEmpty(targetIds)) {
                                    log.info("更新目标执行对象状态，targetIds：{}", targetIds);
                                    weSopExecuteTargetService.update(
                                        WeSopExecuteTarget.builder()
                                            .executeState(3)  // 将状态设为正常结束(3)
                                            .executeSubState(1)  // 将推送状态设为已推送(1)
                                            .build(),
                                        new LambdaQueryWrapper<WeSopExecuteTarget>()
                                            .in(WeSopExecuteTarget::getId, targetIds)
                                    );
                                }
                            }
                        }
                    });
                });


            }else{
                // 根据SOP类型生成不同的消息内容
                if (isPhoneCallSop) {
                    textContent.append("【拨打电话SOP】\r\n");
                    if(isExpiringSoon){
                        textContent.append("以下客户的电话拨打任务剩余10分钟。\r\n");
                    }else{
                        textContent.append(" 今天有" +
                                weCustomerSopPushTaskDto.stream().map(WeSopPushTaskDto::getTargetId).collect(Collectors.toSet()).size()
                                + "个客户需要电话跟进。\r\n");
                    }
                } else {
                    textContent.append("【客户SOP】\r\n");
                    if(isExpiringSoon){
                        textContent.append("以下客户的SOP推送时间剩余10分钟。\r\n");
                    }else{
                        textContent.append(" 今天有" +
                                weCustomerSopPushTaskDto.stream().map(WeSopPushTaskDto::getTargetId).collect(Collectors.toSet()).size()
                                + "个客户SOP待推送。\r\n");
                    }
                }

                // 按sopBaseId分组
                Map<String, List<WeSopPushTaskDto>> sopBaseIdGroupMap = weCustomerSopPushTaskDto.stream()
                        .collect(Collectors.groupingBy(WeSopPushTaskDto::getSopBaseId));
                
                // 遍历每个sopBaseId的任务组，分别触发群发
                sopBaseIdGroupMap.forEach((sopBaseId, tasksForSop) -> {
                    // 按执行人groupBy
                    Map<String, List<WeSopPushTaskDto>> userIdGroupMap = tasksForSop.stream()
                            .collect(Collectors.groupingBy(WeSopPushTaskDto::getExecuteWeUserId));
                    
                    userIdGroupMap.forEach((userId, userTasks) -> {
                        WeAddGroupMessageQuery query = new WeAddGroupMessageQuery();
                        // 设置基本参数
                        query.setMsgSource(8);
                        query.setIsAll(false);
                        query.setAllSend(false);
                        query.setIsTask(0);
                        query.setChatType(1); // 客户类型
                        
                        // 参数对象
                        Map<String, Object> params = new HashMap<>();
                        query.setParams(params);
                        
                        // 生成唯一ID
                        query.setId(IdUtil.getSnowflakeNextId());
                        
                        // 获取附件列表转为模板
                        List<WeSopAttachments> sopAttachmentsList = iWeSopAttachmentsService.listByIds(
                                userTasks.stream()
                                        .map(WeSopPushTaskDto::getSopAttachmentId)
                                        .map(id -> Long.valueOf(id))
                                        .collect(Collectors.toList())
                        );
                        
                        // 将SOP附件转换为消息模板
                        List<WeMessageTemplate> messageTemplates = iWeSopAttachmentsService.weSopAttachmentsToTemplate(sopAttachmentsList);
                        query.setAttachmentsList(messageTemplates);
                        
                        // 如果有文本内容，设置content字段
                        for (WeMessageTemplate attachment : messageTemplates) {
                            if ("text".equals(attachment.getMsgType())) {
                                String content = attachment.getContent();
                                if (StringUtils.isNotEmpty(content)) {
                                    query.setContent(content);
                                    break;
                                }
                            }
                        }
                        
                        // 构建发送人信息
                        List<WeAddGroupMessageQuery.SenderInfo> senderInfos = new ArrayList<WeAddGroupMessageQuery.SenderInfo>();
                        WeAddGroupMessageQuery.SenderInfo senderInfo = new WeAddGroupMessageQuery.SenderInfo();
                        senderInfo.setUserId(userId);
                        
                        List<String> customerList = userTasks.stream()
                                .map(WeSopPushTaskDto::getTargetId)
                                .collect(Collectors.toList());
                        senderInfo.setCustomerList(customerList);
                        
                        senderInfos.add(senderInfo);
                        query.setSenderList(senderInfos);
                        
                        // 设置客户查询条件 - 完整版
                        WeCustomersQuery weCustomersQuery = new WeCustomersQuery();
                        
                        // 基本查询条件
                        weCustomersQuery.setUserIds(userId);
                        weCustomersQuery.setUserNames(userId);
                        weCustomersQuery.setIsJoinBlacklist(1);
                        weCustomersQuery.setDataScope(false);
                        weCustomersQuery.setFilterLossCustomer(false);
                        weCustomersQuery.setNoRepeat(false);
                        weCustomersQuery.setNoTagCheck(false);
                        weCustomersQuery.setTagNumber(0);
                        weCustomersQuery.setIsContain(2); // 默认包含其中一个标签
                        
                        // 获取关联的SOP基础信息
                        WeSopBase sopBase = iWeSopBaseService.getById(sopBaseId);
                        
                        // 如果SOP基础配置中已有标签条件，则优先使用
                        if (sopBase != null && sopBase.getWeCustomersQuery() != null) {
                            WeCustomersQuery baseQuery = sopBase.getWeCustomersQuery();
                            if (StringUtils.isNotEmpty(baseQuery.getExcludeTagIds())){
                                weCustomersQuery.setExcludeTagIds(baseQuery.getExcludeTagIds());
                            }
                            // 处理标签条件
                            if (StringUtils.isNotEmpty(baseQuery.getTagIds())) {
                                weCustomersQuery.setTagIds(baseQuery.getTagIds());
                                weCustomersQuery.setTagNames(baseQuery.getTagNames());
                                weCustomersQuery.setTagNumber(baseQuery.getTagNumber());
                                
                                // 标签包含关系
                                Integer isContain = baseQuery.getIsContain();
                                if (isContain != null) {
                                    weCustomersQuery.setIsContain(isContain);
                                } else {
                                    weCustomersQuery.setIsContain(2); // 默认包含其中一个标签
                                }
                            } else {
                                weCustomersQuery.setIsContain(2); // 默认包含其中一个标签
                            }
                            
                            // 处理执行人员条件
                            if (StringUtils.isNotEmpty(baseQuery.getUserIds())) {
                                weCustomersQuery.setUserIds(baseQuery.getUserIds());
                                weCustomersQuery.setUserNames(baseQuery.getUserNames());
                            }
                            
                            // 处理客户类型条件
                            if (baseQuery.getCustomerType() != null) {
                                weCustomersQuery.setCustomerType(baseQuery.getCustomerType());
                            }
                            
                            // 处理性别条件
                            if (baseQuery.getGender() != null) {
                                weCustomersQuery.setGender(baseQuery.getGender());
                            }
                        } else {
                            weCustomersQuery.setIsContain(2); // 默认包含其中一个标签
                        }
                        
                        // 创建WeCustomersOrGroupQuery对象
                        WeGroupMessageTemplate.WeCustomersOrGroupQuery customersOrGroupQuery = new WeGroupMessageTemplate.WeCustomersOrGroupQuery();
                        customersOrGroupQuery.setWeCustomersQuery(weCustomersQuery);
                        query.setWeCustomersOrGroupQuery(customersOrGroupQuery);
                        
                        // 设置weCustomersQuery
                        query.setWeCustomersQuery(weCustomersQuery);
                        
                        // 设置当前用户信息 - 完整版
                        WeCorpAccount weCorpAccount = iWeCorpAccountService.getCorpAccountByCorpId(null);
                        if (weCorpAccount != null) {
                            LoginUser loginUser = new LoginUser();
                            // 填充loginUser基本信息
                            loginUser.setUserName("超管");
                            loginUser.setUserId(1L);
                            loginUser.setCorpId(weCorpAccount.getCorpId());
                            loginUser.setCorpName(weCorpAccount.getCompanyName());
                            // 生成一个简单的token，与正确格式一致
                            loginUser.setToken(UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32));
                            loginUser.setUserType("00");
                            loginUser.setRoles(new HashSet<>());
                            
                            // 设置额外字段，与正常群发保持一致
                            loginUser.setExpireTime(System.currentTimeMillis() + ********); // 12小时
                            loginUser.setLoginTime(System.currentTimeMillis());
                            loginUser.setIpaddr("**********");
                            
                            // 设置SysUser详细信息
                            SysUser sysUser = new SysUser();
                            sysUser.setUserId(1L);
                            sysUser.setUserName("超管");
                            sysUser.setWeUserId("admin");
                            sysUser.setNickName("admin");
                            sysUser.setCheckIsRoot(false);
                            sysUser.setDataScope(1);
                            sysUser.setDelFlag(0);
                            sysUser.setSex("0");
                            sysUser.setStatus("0");
                            sysUser.setUserType("00");
                            sysUser.setRoles(new ArrayList<>());
                            sysUser.setDeptId(0L);
                            
                            // 添加其他必要字段
                            sysUser.setAvatar("https://iyque-1251309172.cos.ap-nanjing.myqcloud.com/2024/07/03/3df8e83a-8db2-493c-ac88-392d5bc8e4f1.png");
                            sysUser.setEmail("");
                            sysUser.setPhoneNumber("");
                            sysUser.setLoginIp("");
                            sysUser.setPassword("$2a$10$B3bRSFj1XXwv4KxszXQQbOUStlwYiwTIlyllHVU6fd9pjDK/g0XQS");
                            
                            // 设置创建时间
                            Calendar calendar = Calendar.getInstance();
                            calendar.set(2022, Calendar.SEPTEMBER, 7, 14, 24, 47); 
                            sysUser.setCreateTime(calendar.getTime());
                            
                            // 不要尝试设置dept字段，让JSON序列化器处理它
                            // 只需要设置正确的参数
                            sysUser.setParams(new HashMap<>());
                            
                            loginUser.setSysUser(sysUser);
                            query.setLoginUser(loginUser);
                        }

                        // 不要使用反射设置weCustomersQuery
                        // Spring会根据JSON注解处理这个引用
                        // 保持默认的weCustomersQuery设置即可
                        
                        // 设置业务ID列表
                        query.setBusinessIds(
                                String.join(",", userTasks.stream()
                                        .map(WeSopPushTaskDto::getExcuteTargetAttachId)
                                        .collect(Collectors.toList()))
                        );
                        
                        // 发送到立即群发队列
                        rabbitTemplate.convertAndSend(rabbitMQSettingConfig.getWeDelayEx(), 
                                rabbitMQSettingConfig.getWeGroupMsgRk(), 
                                JSONObject.toJSONString(query));
                        
                        // 发送消息后，立即更新任务状态为已执行，防止重复执行
                        List<Long> taskIds = userTasks.stream()
                                .map(WeSopPushTaskDto::getExcuteTargetAttachId)
                                .map(Long::valueOf)
                                .collect(Collectors.toList());
                        
                        if (CollectionUtil.isNotEmpty(taskIds)) {
                            // 更新附件状态
                            this.update(
                                WeSopExecuteTargetAttachments.builder()
                                    .executeState(1)  // 更新为已执行状态
                                    .executeTime(new Date())  // 设置实际执行时间
                                    .isTip(isExpiringSoon ? 2 : 1)  // 设置提醒状态
                                    .build(),
                                new LambdaQueryWrapper<WeSopExecuteTargetAttachments>()
                                    .in(WeSopExecuteTargetAttachments::getId, taskIds)
                            );
                            
                            // 查询并获取所有关联的execute_target_id
                            Set<Long> targetIds = new HashSet<>();
                            List<WeSopExecuteTargetAttachments> attachmentsList = this.list(
                                new LambdaQueryWrapper<WeSopExecuteTargetAttachments>()
                                    .in(WeSopExecuteTargetAttachments::getId, taskIds)
                                    .select(WeSopExecuteTargetAttachments::getExecuteTargetId)
                            );
                            
                            if (CollectionUtil.isNotEmpty(attachmentsList)) {
                                for (WeSopExecuteTargetAttachments attachment : attachmentsList) {
                                    targetIds.add(attachment.getExecuteTargetId());
                                }
                                
                                // 更新目标执行对象状态
                                if (CollectionUtil.isNotEmpty(targetIds)) {
                                    log.info("更新目标执行对象状态，targetIds：{}", targetIds);
                                    weSopExecuteTargetService.update(
                                        WeSopExecuteTarget.builder()
                                            .executeState(3)  // 将状态设为正常结束(3)
                                            .executeSubState(1)  // 将推送状态设为已推送(1)
                                            .build(),
                                        new LambdaQueryWrapper<WeSopExecuteTarget>()
                                            .in(WeSopExecuteTarget::getId, targetIds)
                                    );
                                }
                            }
                        }
                    });
                });
            }

                if(groupOrCustomer){
//
//
//                    List<WeGroup> weGroups = ((WeGroupMapper)iWeGroupService.getBaseMapper()).selectList(
//                            new LambdaQueryWrapper<WeGroup>()
//                                    .in(WeGroup::getChatId, weCustomerSopPushTaskDto.stream().map(WeSopPushTaskDto::getTargetId).collect(Collectors.toList()))
//                                    .last("limit 10")
//                    );
//
//                    if(CollectionUtil.isNotEmpty(weGroups)){
//
//                        weGroups.stream().forEach(weGroup->{
//
//                            WeSopPushTaskDto weSopPushTaskDtoStream =
//                                    weCustomerSopPushTaskDto.stream().filter(task -> task.getTargetId().equals(weGroup.getChatId()) && task.getExecuteWeUserId().equals(executeWeUserId))
//                                            .collect(Collectors.toList()).stream().findFirst().get();
//
//                            if(null !=weSopPushTaskDtoStream){
//                                if(isExpiringSoon){
//                                    textContent.append("【"+weGroup.getGroupName()+"】\r\n");
//                                }else{
//                                    textContent.append("【"+weGroup.getGroupName()+"】"+
//                                            DateUtil.format(weSopPushTaskDtoStream.getPushStartTime(),"HH:mm:ss")+"——"+DateUtil.format(weSopPushTaskDtoStream.getPushEndTime(),"HH:mm:ss")+"\r\n");
//
//                                }
//
//                            }
//
//                        });

                        if(isExpiringSoon){
                            textContent.append(" 请注意及时推送。");
                            iWeMessagePushService.pushMessageSelfH5(
                                    ListUtil.toList(executeWeUserId),textContent.toString(),groupOrCustomer?MessageNoticeType.GROUP_SOP_DQTX.getType():MessageNoticeType.CUSTOMER_SOP_DQTX.getType(),true,null
                            );

                        }else{
                            iWeMessagePushService.pushMessageSelfH5(
                                    ListUtil.toList(executeWeUserId),textContent.toString(),groupOrCustomer?MessageNoticeType.GROUP_SOP.getType():MessageNoticeType.CUSTOMER_SOP.getType(),true,null
                            );

                            //客群SOP代办任务
                            WeTasksRequest build = WeTasksRequest.builder().weUserId(executeWeUserId).content(textContent.toString()).build();
                            weTasksService.addGroupSop(build);

                        }



                }else{
                    // 客户SOP逻辑，包括拨打电话SOP
                    if (isPhoneCallSop) {
                        // 拨打电话SOP的混合消息内容
                        textContent.append("【拨打电话SOP】\r\n");
                        if(isExpiringSoon){
                            textContent.append("以下客户的SOP推送时间剩余10分钟。\r\n");
                        }else{
                            textContent.append(" 今天有" +
                                    weCustomerSopPushTaskDto.stream().map(WeSopPushTaskDto::getTargetId).collect(Collectors.toSet()).size()
                                    + "个客户SOP待推送。\r\n");
                        }
                    } else {
                        // 普通客户SOP逻辑
                        textContent.append("【客户SOP】\r\n");
                        if(isExpiringSoon){
                            textContent.append("以下客户的SOP推送时间剩余10分钟。\r\n");
                        }else{
                            textContent.append(" 今天有" +
                                    weCustomerSopPushTaskDto.stream().map(WeSopPushTaskDto::getTargetId).collect(Collectors.toSet()).size()
                                    + "个客户SOP待推送。\r\n");
                        }
                    }

//                    List<WeCustomer> weCustomers =((WeCustomerMapper)iWeCustomerService.getBaseMapper()).selectList(
//                            new LambdaQueryWrapper<WeCustomer>()
//                                    .eq(WeCustomer::getAddUserId,executeWeUserId)
//                                    .in(WeCustomer::getExternalUserid, weCustomerSopPushTaskDto.stream().map(WeSopPushTaskDto::getTargetId).collect(Collectors.toList()))
//                                    .last("limit 10")
//                    );
//
//
//                    if(CollectionUtil.isNotEmpty(weCustomers)){
//
//                        weCustomers.stream().forEach(kk->{
//                            WeSopPushTaskDto weSopPushTaskDtoStream =
//                                    weCustomerSopPushTaskDto.stream().filter(task -> task.getTargetId().equals(kk.getExternalUserid()) && task.getExecuteWeUserId().equals(executeWeUserId))
//                                            .collect(Collectors.toList()).stream().findFirst().get();
//
//                            if(null != weSopPushTaskDtoStream){
//                                if(isExpiringSoon){
//                                    textContent.append("【"+kk.getCustomerName()+"】\r\n");
//                                }else{
//                                    textContent.append("【"+kk.getCustomerName()+"】"+
//                                            DateUtil.format(weSopPushTaskDtoStream.getPushStartTime(),"HH:mm:ss")+"——"+DateUtil.format(weSopPushTaskDtoStream.getPushEndTime(),"HH:mm:ss")+"\r\n");
//
//                                }
//
//                            }
//
//                        });

                        if(isExpiringSoon){
                            textContent.append(" 请注意及时推送。");
                            // 根据SOP类型选择消息类型
                            Integer messageType;
                            if (groupOrCustomer) {
                                messageType = MessageNoticeType.GROUP_SOP_DQTX.getType();
                            } else if (isPhoneCallSop) {
                                messageType = MessageNoticeType.PHONE_CALL_SOP.getType(); // 拨打电话SOP使用专门的类型
                            } else {
                                messageType = MessageNoticeType.CUSTOMER_SOP_DQTX.getType();
                            }

                            log.info("[PHONE_CALL_SOP_DEBUG] 推送消息类型: {}, 内容: {}", messageType, textContent.toString());
                            iWeMessagePushService.pushMessageSelfH5(
                                    ListUtil.toList(executeWeUserId), textContent.toString(), messageType, true, null
                            );
                        }else{
                            // 根据SOP类型选择消息类型
                            Integer messageType;
                            if (groupOrCustomer) {
                                messageType = MessageNoticeType.GROUP_SOP.getType();
                            } else if (isPhoneCallSop) {
                                messageType = MessageNoticeType.PHONE_CALL_SOP.getType(); // 拨打电话SOP使用专门的类型
                            } else {
                                messageType = MessageNoticeType.CUSTOMER_SOP.getType();
                            }

                            log.info("[PHONE_CALL_SOP_DEBUG] 原有逻辑 - 推送消息类型: {}, 内容: {}", messageType, textContent.toString());
                            log.info("[PHONE_CALL_SOP_DEBUG] 原有逻辑 - 这是第二次消息发送（混合消息）");
                            iWeMessagePushService.pushMessageSelfH5(
                                    ListUtil.toList(executeWeUserId), textContent.toString(), messageType, true, null
                            );

                            // 如果是拨打电话SOP，增加消息计数
                            if (isPhoneCallSop) {
                                int currentCount = phoneCallSopMessageCount.get();
                                phoneCallSopMessageCount.set(currentCount + 1);
                                log.info("[PHONE_CALL_SOP_DEBUG] 第二次消息发送完成，当前消息计数: {}", phoneCallSopMessageCount.get());
                            }

                            // 根据SOP类型创建不同的代办任务
                            if (!isPhoneCallSop) {
                                // 普通客户SOP，创建一个任务（拨打电话SOP的任务已在handlePhoneCallSopByGroup中创建）
                                WeTasksRequest build = WeTasksRequest.builder()
                                        .weUserId(executeWeUserId)
                                        .content(textContent.toString())
                                        .build();

                                weTasksService.addCustomerSop(build);
                            } else {
                                log.info("[PHONE_CALL_SOP_DEBUG] 拨打电话SOP的任务已在handlePhoneCallSopByGroup中创建，跳过重复创建");
                            }
                        }
            }

            //设置为已提示（排除拨打电话SOP，因为已在handlePhoneCallSopByGroup中更新）
            List<String> allTaskIds = weCustomerSopPushTaskDto.stream()
                    .map(WeSopPushTaskDto::getExcuteTargetAttachId)
                    .collect(Collectors.toList());

            if (isPhoneCallSop) {
                log.info("[PHONE_CALL_SOP_DEBUG] 拨打电话SOP任务状态已在handlePhoneCallSopByGroup中更新，跳过重复更新");
            } else {
                this.update(
                        WeSopExecuteTargetAttachments.builder()
                                .isTip(isExpiringSoon ? 2 : 1)
                                .build(),
                        new LambdaQueryWrapper<WeSopExecuteTargetAttachments>()
                                .in(WeSopExecuteTargetAttachments::getId, allTaskIds)
                );
                log.info("[PHONE_CALL_SOP_DEBUG] 已更新{}个普通SOP任务状态为已提示", allTaskIds.size());
            }
        }

        // 输出拨打电话SOP消息发送统计
        Integer finalMessageCount = phoneCallSopMessageCount.get();
        if (finalMessageCount != null && finalMessageCount > 0) {
            log.info("[PHONE_CALL_SOP_DEBUG] ===== 拨打电话SOP消息发送统计 =====");
            log.info("[PHONE_CALL_SOP_DEBUG] 总共发送了 {} 条消息", finalMessageCount);
            log.info("[PHONE_CALL_SOP_DEBUG] 期望发送 2 条消息（1条独立消息 + 1条混合消息）");
            if (finalMessageCount < 2) {
                log.warn("[PHONE_CALL_SOP_DEBUG] 警告：消息发送数量不足，可能存在问题！");
            }
            log.info("[PHONE_CALL_SOP_DEBUG] =====================================");
        }

        // 清理ThreadLocal
        phoneCallSopMessageCount.remove();
    }

    /**
     * 处理拨打电话SOP，按sopBaseId+executeTargetAttachId分组，每个时间段发送独立消息
     */
    private void handlePhoneCallSopByGroup(String executeWeUserId, List<WeSopPushTaskDto> weCustomerSopPushTaskDto, boolean isExpiringSoon) {
        log.info("[PHONE_CALL_SOP_DEBUG] 开始按sopBaseId+executeTargetAttachId分组处理拨打电话SOP，总任务数: {}", weCustomerSopPushTaskDto.size());

        // 先打印所有原始数据，用于调试
        log.info("[PHONE_CALL_SOP_DEBUG] === 原始数据详情 ===");
        for (int i = 0; i < weCustomerSopPushTaskDto.size(); i++) {
            WeSopPushTaskDto task = weCustomerSopPushTaskDto.get(i);
            log.info("[PHONE_CALL_SOP_DEBUG] 任务[{}]: sopBaseId={}, executeTargetAttachId={}, targetId={}, sopAttachmentId={}",
                    i, task.getSopBaseId(), task.getExcuteTargetAttachId(), task.getTargetId(), task.getSopAttachmentId());
        }

        // 按sopBaseId+pushStartTime+pushEndTime分组，确保相同时间段的客户合并到一条消息
        // executeTargetAttachId是每个客户每个时间段的唯一ID，不能用于分组
        Map<String, List<WeSopPushTaskDto>> timeSlotGroupMap = weCustomerSopPushTaskDto.stream()
                .collect(Collectors.groupingBy(task -> {
                    String sopBaseId = task.getSopBaseId() != null ? task.getSopBaseId() : "null";
                    // 使用日期格式化，确保相同时间段的任务被分到一组
                    String pushStartTime = task.getPushStartTime() != null ?
                            new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(task.getPushStartTime()) : "null";
                    String pushEndTime = task.getPushEndTime() != null ?
                            new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(task.getPushEndTime()) : "null";
                    String groupKey = sopBaseId + "_" + pushStartTime + "_" + pushEndTime;
                    log.debug("[PHONE_CALL_SOP_DEBUG] 生成分组键: {} (sopBaseId={}, pushStartTime={}, pushEndTime={})",
                            groupKey, sopBaseId, pushStartTime, pushEndTime);
                    return groupKey;
                }));

        log.info("[PHONE_CALL_SOP_DEBUG] 分组后的时间段数量: {}", timeSlotGroupMap.size());
        timeSlotGroupMap.forEach((groupKey, tasks) -> {
            String sopBaseId = tasks.get(0).getSopBaseId();
            // 选择第一个executeTargetAttachId作为代表，用于前端链接
            String executeTargetAttachId = tasks.get(0).getExcuteTargetAttachId();
            String pushStartTime = tasks.get(0).getPushStartTime() != null ?
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(tasks.get(0).getPushStartTime()) : "null";
            String pushEndTime = tasks.get(0).getPushEndTime() != null ?
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(tasks.get(0).getPushEndTime()) : "null";
            log.info("[PHONE_CALL_SOP_DEBUG] 待处理时间段: groupKey={}, sopBaseId={}, 时间段=[{} - {}], 代表executeTargetAttachId={}, 任务数: {}",
                    groupKey, sopBaseId, pushStartTime, pushEndTime, executeTargetAttachId, tasks.size());

            // 打印该组内所有任务的详细信息
            for (int i = 0; i < tasks.size(); i++) {
                WeSopPushTaskDto task = tasks.get(i);
                log.info("[PHONE_CALL_SOP_DEBUG]   组内任务[{}]: targetId={}, executeTargetAttachId={}, sopAttachmentId={}",
                        i, task.getTargetId(), task.getExcuteTargetAttachId(), task.getSopAttachmentId());
            }
        });

        // 为每个时间段单独处理和发送消息
        timeSlotGroupMap.forEach((groupKey, tasksForTimeSlot) -> {
            // 在try块外部定义变量，确保catch块可以访问
            String sopBaseId = tasksForTimeSlot.get(0).getSopBaseId();
            String executeTargetAttachId = tasksForTimeSlot.get(0).getExcuteTargetAttachId();
            try {
                log.info("[PHONE_CALL_SOP_DEBUG] 处理时间段: sopBaseId={}, executeTargetAttachId={}, 任务数量: {}",
                        sopBaseId, executeTargetAttachId, tasksForTimeSlot.size());

            // 获取SOP名称
            String sopName = "拨打电话SOP";
            try {
                WeSopBase sopBase = iWeSopBaseService.getById(sopBaseId);
                if (sopBase != null) {
                    sopName = sopBase.getSopName();
                }
            } catch (Exception e) {
                log.warn("[PHONE_CALL_SOP_DEBUG] 获取SOP名称失败，使用默认名称，sopBaseId: {}", sopBaseId, e);
            }

            // 构建消息内容
            StringBuilder textContent = new StringBuilder();
            textContent.append("【").append(sopName).append("】\r\n");

            if (isExpiringSoon) {
                textContent.append("以下客户的SOP推送时间剩余10分钟。\r\n");
            } else {
                textContent.append(" 今天有")
                        .append(tasksForTimeSlot.stream().map(WeSopPushTaskDto::getTargetId).collect(Collectors.toSet()).size())
                        .append("个客户SOP待推送。\r\n");
            }

            // 发送消息（拨打电话SOP无论是否即将到期都使用相同的消息类型）
            Integer messageType = MessageNoticeType.PHONE_CALL_SOP.getType();

            log.info("[PHONE_CALL_SOP_DEBUG] handlePhoneCallSopByGroup - 发送消息 - SOP: {}, executeTargetAttachId: {}, 消息类型: {}, 内容: {}",
                    sopName, executeTargetAttachId, messageType, textContent.toString());
            log.info("[PHONE_CALL_SOP_DEBUG] handlePhoneCallSopByGroup - 发送时间段独立消息，sopBaseId: {}, executeTargetAttachId: {}",
                    sopBaseId, executeTargetAttachId);

            // 使用专门的方法发送拨打电话SOP消息，包含sopBaseId和executeTargetAttachId参数
            // 对于按时间段分组的情况，我们传递第一个executeTargetAttachId作为代表
            iWeMessagePushService.pushPhoneCallSopMessage(
                    ListUtil.toList(executeWeUserId),
                    textContent.toString(),
                    messageType,
                    sopBaseId,
                    executeTargetAttachId // 传递代表性的executeTargetAttachId
            );

            // 增加消息计数
            int currentCount = phoneCallSopMessageCount.get();
            phoneCallSopMessageCount.set(currentCount + 1);
            log.info("[PHONE_CALL_SOP_DEBUG] 第一次消息发送完成，当前消息计数: {}", phoneCallSopMessageCount.get());

            // 创建任务
            WeTasksRequest build = WeTasksRequest.builder()
                    .weUserId(executeWeUserId)
                    .content(textContent.toString())
                    .sopBaseId(sopBaseId) // 传递sopBaseId用于独立显示
                    .executeTargetAttachId(executeTargetAttachId) // 传递executeTargetAttachId用于时间段区分
                    .build();

            weTasksService.addPhoneCallSop(build);

                log.info("[PHONE_CALL_SOP_DEBUG] 时间段处理完成: sopBaseId={}, executeTargetAttachId={}", sopBaseId, executeTargetAttachId);
            } catch (Exception e) {
                log.error("[PHONE_CALL_SOP_DEBUG] 处理时间段时发生异常: sopBaseId={}, executeTargetAttachId={}, error={}",
                        sopBaseId, executeTargetAttachId, e.getMessage(), e);
            }
        });

        log.info("[PHONE_CALL_SOP_DEBUG] 所有拨打电话SOP时间段处理完成，共处理了{}个时间段", timeSlotGroupMap.size());
        timeSlotGroupMap.forEach((groupKey, tasks) -> {
            String sopBaseId = tasks.get(0).getSopBaseId();
            String executeTargetAttachId = tasks.get(0).getExcuteTargetAttachId();
            log.info("[PHONE_CALL_SOP_DEBUG] 已处理时间段: sopBaseId={}, executeTargetAttachId={}, 任务数: {}",
                    sopBaseId, executeTargetAttachId, tasks.size());
        });

        // 更新拨打电话SOP任务状态，避免重复处理（补充完整的状态更新逻辑）
        List<String> processedTaskIds = timeSlotGroupMap.values().stream()
                .flatMap(List::stream)
                .map(WeSopPushTaskDto::getExcuteTargetAttachId)
                .collect(Collectors.toList());

        if (!processedTaskIds.isEmpty()) {
            // 转换为Long类型的ID列表
            List<Long> taskIds = processedTaskIds.stream()
                    .map(Long::valueOf)
                    .collect(Collectors.toList());

            // 更新附件状态（补充缺失的executeState和executeTime）
            this.update(
                    WeSopExecuteTargetAttachments.builder()
                            .executeState(1)  // 更新为已执行状态
                            .executeTime(new Date())  // 设置实际执行时间
                            .isTip(isExpiringSoon ? 2 : 1)  // 设置提醒状态
                            .build(),
                    new LambdaQueryWrapper<WeSopExecuteTargetAttachments>()
                            .in(WeSopExecuteTargetAttachments::getId, taskIds)
            );

            // 查询并获取所有关联的execute_target_id（补充缺失的目标状态更新）
            Set<Long> targetIds = new HashSet<>();
            List<WeSopExecuteTargetAttachments> attachmentsList = this.list(
                new LambdaQueryWrapper<WeSopExecuteTargetAttachments>()
                    .in(WeSopExecuteTargetAttachments::getId, taskIds)
                    .select(WeSopExecuteTargetAttachments::getExecuteTargetId)
            );

            if (CollectionUtil.isNotEmpty(attachmentsList)) {
                for (WeSopExecuteTargetAttachments attachment : attachmentsList) {
                    targetIds.add(attachment.getExecuteTargetId());
                }

                // 更新目标执行对象状态并检查是否应该结束SOP（与新客SOP逻辑一致）
                if (CollectionUtil.isNotEmpty(targetIds)) {
                    log.info("[PHONE_CALL_SOP_DEBUG] 更新目标执行对象状态，targetIds：{}", targetIds);

                    // 获取需要更新的执行目标对象
                    List<WeSopExecuteTarget> executeTargets = weSopExecuteTargetService.list(
                        new LambdaQueryWrapper<WeSopExecuteTarget>()
                            .in(WeSopExecuteTarget::getId, targetIds)
                    );

                    // 为每个执行目标检查是否应该结束SOP（与新客SOP逻辑完全一致）
                    executeTargets.forEach(executeTarget -> {
                        // 检查该客户的所有任务是否都已完成
                        long unfinishedCount = this.count(
                            new LambdaQueryWrapper<WeSopExecuteTargetAttachments>()
                                .eq(WeSopExecuteTargetAttachments::getExecuteTargetId, executeTarget.getId())
                                .eq(WeSopExecuteTargetAttachments::getExecuteState, 0)  // 未执行的任务
                                .eq(WeSopExecuteTargetAttachments::getDelFlag, 0)  // 未删除的任务
                        );

                        log.info("[PHONE_CALL_SOP_DEBUG] 客户剩余未完成任务数量: {}, executeTargetId: {}", unfinishedCount, executeTarget.getId());

                        // 如果所有任务都完成了，设置结束时间和状态（与新客SOP逻辑完全一致）
                        if (unfinishedCount == 0) {
                            executeTarget.setExecuteEndTime(new Date());
                            executeTarget.setExecuteState(3);  // 正常结束
                            executeTarget.setExecuteSubState(1);  // 已推送
                            log.info("[PHONE_CALL_SOP_DEBUG] SOP已正常结束，executeTargetId: {}", executeTarget.getId());
                        } else {
                            // 还有未完成的任务，只更新推送状态
                            executeTarget.setExecuteState(3);  // 正常结束
                            executeTarget.setExecuteSubState(1);  // 已推送
                            log.info("[PHONE_CALL_SOP_DEBUG] SOP尚未完成，还有{}个任务待执行", unfinishedCount);
                        }
                    });

                    // 批量更新执行目标状态
                    weSopExecuteTargetService.updateBatchById(executeTargets);
                }
            }

            log.info("[PHONE_CALL_SOP_DEBUG] 已完整更新{}个拨打电话SOP任务状态", processedTaskIds.size());
        }
    }


}
