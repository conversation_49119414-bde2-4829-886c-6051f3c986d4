{"remainingRequest": "D:\\project\\scrm\\front\\mobile2\\node_modules\\thread-loader\\dist\\cjs.js!D:\\project\\scrm\\front\\mobile2\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue", "mtime": 1753168001346}, {"path": "D:\\project\\scrm\\front\\mobile2\\babel.config.js", "mtime": 1750650120014}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751130691202}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751130691203}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751130701171}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751130691202}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751130703090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}