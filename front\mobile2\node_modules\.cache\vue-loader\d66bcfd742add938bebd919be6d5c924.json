{"remainingRequest": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue", "mtime": 1753168001346}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751130701171}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751130691202}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751130703090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}