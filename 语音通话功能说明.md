# 拨打电话SOP页面 - 语音通话功能

## 功能概述

在拨打电话SOP页面中新增了企业微信语音通话功能，用户可以直接通过企业微信与客户进行语音通话。

## 实现方案

### 1. 前端界面修改

- **位置**: `front/mobile2/src/views/Applications/communityOperations/swarmsSOP/swarmsSOP.vue`
- **修改内容**:
  - 在客户操作区域添加了"语音通话"按钮
  - 按钮仅在客户有 `externalUserid` 时显示
  - 采用垂直布局，将"拨打电话"和"语音通话"按钮排列

### 2. 功能实现

#### 主要方法: `makeVoiceCallForCustomer(customer)`

与拨打电话功能保持一致的逻辑：

```javascript
async makeVoiceCallForCustomer(customer) {
  // 1. 验证客户信息和状态
  if (!customer.externalUserid) {
    this.$toast('客户信息不完整，无法发起语音通话')
    return
  }

  // 2. 检查是否已经通话过
  if (customer.executeState === 1) {
    this.$toast('该客户已发起过通话')
    return
  }

  // 3. 检查企业微信环境
  if (!window.wx || !window.wx.invoke) {
    this.$toast('请在企业微信中使用语音通话功能')
    return
  }

  try {
    // 4. 记录语音通话状态
    await this.recordVoiceCall(customer)

    // 5. 刷新数据状态
    await this.getData(this.tabBar, customer.executeTargetAttachId)

    // 6. 打开语音通话窗口
    this.openVoiceChatWindow(customer)
  } catch (error) {
    // 错误处理
  }
}
```

#### 辅助方法:

1. **`recordVoiceCall(customer)`**: 记录语音通话状态到后端
2. **`openVoiceChatWindow(customer)`**: 打开企业微信聊天窗口

### 3. 状态管理

与拨打电话按钮完全一致的状态逻辑：

- **按钮状态**: 根据 `customer.executeState` 显示不同状态
  - `executeState === 0`: 显示"语音通话"，按钮可点击
  - `executeState === 1`: 显示"已通话"，按钮禁用
- **状态记录**: 点击后调用后端API记录通话状态
- **数据同步**: 操作后重新获取后端数据确保状态一致性
- **标签页切换**: 支持自动切换到"已拨打"标签页

### 4. 样式设计

- **按钮布局**: 使用 `.action-buttons` 容器，垂直排列两个按钮
- **语音通话按钮**: 绿色渐变背景 (#4CAF50 到 #45a049)
- **拨打电话按钮**: 保持原有橙色渐变背景
- **禁用状态**: 已通话的按钮显示为灰色，不可点击
- **交互效果**: 悬停时按钮上移，带有阴影效果

### 5. 技术依赖

- **企业微信JSAPI**: 使用 `openEnterpriseChat` 接口
- **后端API**: 复用现有的 `recordPhoneCall` 接口记录通话状态
- **权限配置**: 已在 `front/mobile2/src/router/index.js` 中配置相关权限

## 使用流程

1. 用户在拨打电话SOP页面看到客户列表
2. 对于有企业微信外部联系人ID的客户，显示"语音通话"按钮
3. 点击"语音通话"按钮（仅在未通话状态下可点击）
4. 系统记录通话状态到后端
5. 按钮状态更新为"已通话"并禁用
6. 系统调用企业微信API打开与该客户的聊天窗口
7. 用户可在聊天窗口中发起语音通话

## 优势特点

1. **逻辑一致性**: 与拨打电话功能保持完全一致的状态管理逻辑
2. **最小代码修改**: 仅在现有页面添加新按钮和方法，不影响原有功能
3. **状态同步**: 支持状态记录、数据刷新和标签页切换
4. **用户体验优化**: 直接在企业微信内完成语音通话，无需切换应用
5. **权限安全**: 基于企业微信现有权限体系，安全可靠
6. **界面美观**: 与现有设计风格保持一致，支持禁用状态显示

## 注意事项

1. 功能仅在企业微信环境中可用
2. 需要客户具有有效的 `externalUserid`
3. 需要企业微信版本支持 `openEnterpriseChat` API
4. 语音通话的具体操作在企业微信聊天窗口中完成

## 功能对比

| 特性 | 拨打电话按钮 | 语音通话按钮 |
|------|-------------|-------------|
| **显示条件** | `customer.customerPhone` 存在 | `customer.externalUserid` 存在 |
| **状态检查** | `executeState === 1` 时禁用 | `executeState === 1` 时禁用 |
| **按钮文本** | "拨打电话" → "已拨打" | "语音通话" → "已通话" |
| **记录方法** | `recordPhoneCall()` | `recordVoiceCall()` |
| **调用方式** | `callMethod: 'mobile'` | `callMethod: 'wechat_voice'` |
| **执行动作** | 调用系统拨号器 | 打开企业微信聊天窗口 |
| **数据刷新** | ✅ 支持 | ✅ 支持 |
| **标签页切换** | ✅ 支持 | ✅ 支持 |
| **调试日志** | ✅ 支持 | ✅ 支持 |

## 兼容性

- 完全兼容现有的拨打电话功能
- 不影响其他SOP功能
- 支持调试面板记录操作日志
- 状态管理与拨打电话功能保持一致
