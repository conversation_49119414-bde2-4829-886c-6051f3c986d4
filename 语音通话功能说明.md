# 拨打电话SOP页面 - 语音通话功能

## 功能概述

在拨打电话SOP页面中新增了企业微信语音通话功能，用户可以直接通过企业微信与客户进行语音通话。

## 实现方案

### 1. 前端界面修改

- **位置**: `front/mobile2/src/views/Applications/communityOperations/swarmsSOP/swarmsSOP.vue`
- **修改内容**:
  - 在客户操作区域添加了"语音通话"按钮
  - 按钮仅在客户有 `externalUserid` 时显示
  - 采用垂直布局，将"拨打电话"和"语音通话"按钮排列

### 2. 功能实现

#### 新增方法: `makeVoiceCallForCustomer(customer)`

```javascript
makeVoiceCallForCustomer(customer) {
  // 1. 验证客户信息
  if (!customer.externalUserid) {
    this.$toast('客户信息不完整，无法发起语音通话')
    return
  }

  // 2. 检查企业微信环境
  if (!window.wx || !window.wx.invoke) {
    this.$toast('请在企业微信中使用语音通话功能')
    return
  }

  // 3. 调用企业微信API打开聊天窗口
  window.wx.invoke('openEnterpriseChat', {
    userIds: sessionStorage.userId || '',
    externalUserIds: customer.externalUserid,
    groupName: '',
    chatId: '',
    success: (res) => {
      this.$toast('已打开聊天窗口，可在其中发起语音通话')
    },
    fail: (res) => {
      // 错误处理
    }
  })
}
```

### 3. 样式设计

- **按钮布局**: 使用 `.action-buttons` 容器，垂直排列两个按钮
- **语音通话按钮**: 绿色渐变背景 (#4CAF50 到 #45a049)
- **拨打电话按钮**: 保持原有橙色渐变背景
- **交互效果**: 悬停时按钮上移，带有阴影效果

### 4. 技术依赖

- **企业微信JSAPI**: 使用 `openEnterpriseChat` 接口
- **权限配置**: 已在 `front/mobile2/src/router/index.js` 中配置相关权限

## 使用流程

1. 用户在拨打电话SOP页面看到客户列表
2. 对于有企业微信外部联系人ID的客户，显示"语音通话"按钮
3. 点击"语音通话"按钮
4. 系统调用企业微信API打开与该客户的聊天窗口
5. 用户可在聊天窗口中发起语音通话

## 优势特点

1. **最小代码修改**: 仅在现有页面添加新按钮和方法，不影响原有功能
2. **用户体验优化**: 直接在企业微信内完成语音通话，无需切换应用
3. **权限安全**: 基于企业微信现有权限体系，安全可靠
4. **界面美观**: 与现有设计风格保持一致

## 注意事项

1. 功能仅在企业微信环境中可用
2. 需要客户具有有效的 `externalUserid`
3. 需要企业微信版本支持 `openEnterpriseChat` API
4. 语音通话的具体操作在企业微信聊天窗口中完成

## 兼容性

- 兼容现有的拨打电话功能
- 不影响其他SOP功能
- 支持调试面板记录操作日志
